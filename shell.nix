{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Build tools
    gnumake
    cmake
    gcc
    pkg-config
    
    # Qt6 development
    qt6.full
    qt6.qtbase
    qt6.qttools
    qt6.qmake
    
    # xmake
    xmake
    
    # Additional libraries that might be needed
    libGL
    libGLU
    xorg.libX11
    xorg.libXext
    xorg.libXi
    xorg.libXtst
    xorg.libXrandr
    wayland
    wayland-protocols
    libxkbcommon
  ];

  shellHook = ''
    echo "Qt6 development environment loaded"
    echo "Available Qt version: $(qmake -query QT_VERSION)"
    export QT_QPA_PLATFORM_PLUGIN_PATH="${pkgs.qt6.qtbase}/lib/qt-6/plugins/platforms"
    export QML2_IMPORT_PATH="${pkgs.qt6.qtbase}/lib/qt-6/qml"
    export QT_PLUGIN_PATH="${pkgs.qt6.qtbase}/lib/qt-6/plugins"
    
    # Force X11 backend for Qt applications (needed for window positioning)
    export QT_QPA_PLATFORM=xcb
    
    echo "Note: QT_QPA_PLATFORM is set to 'xcb' to enable window positioning"
    echo "Make sure you have X11/XWayland running for this to work"
  '';
}
