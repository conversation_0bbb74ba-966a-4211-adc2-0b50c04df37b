-- xmake version
set_xmakever("2.7.1")

-- project
set_project("ArrowKey")

-- set project version
set_version("1.0.0")

-- set language: c++17
set_languages("c++17")

-- add rules
add_rules("mode.debug", "mode.release")

-- target
target("ArrowKey")
    set_kind("binary")
    
    -- enable Qt application with widgets
    add_rules("qt.widgetapp")
    
    -- add source files
    add_files("main.cpp")
    add_files("mainwindow.cpp")
    
    -- add header files (this is important for MOC)
    add_files("mainwindow.h")
    
    -- add ui files
    add_files("mainwindow.ui")
    
    -- set target directory
    set_targetdir("build")
    
    -- add defines for Qt6
    add_defines("QT_DISABLE_DEPRECATED_BEFORE=0x060000")
    
    -- ensure Qt6 modules are linked
    add_frameworks("QtCore", "QtGui", "QtWidgets")
    
    -- platform specific settings
    if is_plat("linux") then
        add_syslinks("pthread")
    end
    
    -- debug mode settings
    if is_mode("debug") then
        set_symbols("debug")
        set_optimize("none")
        add_defines("DEBUG")
    end
    
    -- release mode settings
    if is_mode("release") then
        set_symbols("hidden")
        set_optimize("fastest")
        set_strip("all")
        add_defines("NDEBUG")
    end
