# ArrowKey

## What we used for this project
Qt 6.x: https://www.qt.io/

This is test program for moving window with arrow keys and DVD screensaver effect.

## Requirements
- Qt 6.0 or later
- C++17 compatible compiler
- xmake 2.7.1 or later (for xmake build)
- CMake 3.16 or later (for CMake build)

## Platform Notes
**Wayland Limitation**: Due to Wayland's security model, window positioning functions may not work. To use all features:

1. **Force X11 backend** (Recommended):
   ```bash
   export QT_QPA_PLATFORM=xcb
   ./ArrowKey
   ```

2. **Use X11 session**: Log out and select X11/Xorg session instead of Wayland

3. **XWayland**: Make sure XWayland is installed and running

## Build Instructions

### Using NixOS/Nix Flakes (Recommended for NixOS)
```bash
# Enter development shell
nix develop

# Build with xmake
xmake

# Run
xmake run ArrowKey
```

### Using NixOS/Nix (traditional)
```bash
# Enter development shell
nix-shell

# Build with qmake
qmake arrowkey.pro
make

# Or build with xmake
xmake

# Run
./ArrowKey
```

### Using xmake (after entering nix-shell)
```bash
# Build
xmake

# Run
xmake run ArrowKey
```

### Using CMake
```bash
# Create build directory
mkdir build && cd build

# Configure
cmake ..

# Build
cmake --build .

# Run
./ArrowKey
```

### Using qmake (traditional Qt build)
```bash
# Generate Makefile
qmake arrowkey.pro

# Build
make

# Run
./ArrowKey
```

## Features
- Move window using arrow buttons
- Random position button
- DVD screensaver mode toggle
- Qt6 compatible
