{
  description = "ArrowKey Qt6 application";

  inputs = {
    nixpkgs.url = "github:NixOS/nixpkgs/nixos-unstable";
    flake-utils.url = "github:numtide/flake-utils";
  };

  outputs = { self, nixpkgs, flake-utils }:
    flake-utils.lib.eachDefaultSystem (system:
      let
        pkgs = nixpkgs.legacyPackages.${system};
      in
      {
        devShells.default = pkgs.mkShell {
          buildInputs = with pkgs; [
            # Build tools
            gnumake
            cmake
            gcc
            pkg-config
            
            # Qt6 development
            qt6.full
            qt6.qtbase
            qt6.qttools
            qt6.qmake
            
            # xmake
            xmake
            
            # Additional libraries
            libGL
            libGLU
            xorg.libX11
            xorg.libXext
            xorg.libXi
            xorg.libXtst
            xorg.libXrandr
            wayland
            wayland-protocols
            libxkbcommon
          ];

          shellHook = ''
            echo "ArrowKey Qt6 development environment loaded"
            echo "Available Qt version: $(qmake -query QT_VERSION)"
            export QT_QPA_PLATFORM_PLUGIN_PATH="${pkgs.qt6.qtbase}/lib/qt-6/plugins/platforms"
            export QML2_IMPORT_PATH="${pkgs.qt6.qtbase}/lib/qt-6/qml"
            export QT_PLUGIN_PATH="${pkgs.qt6.qtbase}/lib/qt-6/plugins"
            
            # Force X11 backend for Qt applications (needed for window positioning)
            export QT_QPA_PLATFORM=xcb
            
            echo "Note: QT_QPA_PLATFORM is set to 'xcb' to enable window positioning"
            echo "Make sure you have X11/XWayland running for this to work"
          '';
        };

        packages.default = pkgs.stdenv.mkDerivation {
          pname = "arrowkey";
          version = "1.0.0";

          src = ./.;

          nativeBuildInputs = with pkgs; [
            cmake
            qt6.qttools
            qt6.qmake
          ];

          buildInputs = with pkgs; [
            qt6.qtbase
            qt6.qttools
          ];

          configurePhase = ''
            qmake arrowkey.pro
          '';

          buildPhase = ''
            make
          '';

          installPhase = ''
            mkdir -p $out/bin
            cp ArrowKey $out/bin/
          '';
        };
      }
    );
}
