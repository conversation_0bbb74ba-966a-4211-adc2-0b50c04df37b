name: BuildAndRelease

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v2

    - name: Startup
      run: |
        sudo apt update && sudo apt upgrade -y && sudo apt install build-essential make git -y

    - name: Install Qt
      run: |
        sudo apt install qtbase5-dev qttools5-dev-tools qt5-default

    - name: Build
      run: |
        qmake arrowkey.pro -spec linux-g++ && make qmake_all && make

    - name: Create release
      id: create_release
      uses: actions/create-release@v1.0.0
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: tests
        release_name: Release
        draft: false
        prerelease: false

    - name: Upload Release Asset
      id: upload-release-asset
      uses: actions/upload-release-asset@v1.0.1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: arrowkey
        asset_name: arrowkey
        asset_content_type: application/octet-stream
